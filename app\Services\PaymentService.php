<?php

namespace App\Services;

use Exception;

class PaymentService
{
    private $staxApiUrl;
    private $staxApiKey;

    public function __construct()
    {
        $this->staxApiUrl = config('services.stax.api_url', 'https://apiprod.fattlabs.com/charge');
        $this->staxApiKey = config('services.stax.api_key', env('STAX_API_KEY'));
    }

    /**
     * Process payment with Stax API
     */
    public function processStaxPayment($paymentMethodId, $orderData, $clubs, $coupon = null)
    {
        try {
            // Validate payment method ID
            if (empty($paymentMethodId)) {
                return [
                    'success' => false,
                    'message' => 'Payment method is required'
                ];
            }

            // Validate API key
            if (empty($this->staxApiKey)) {
                return [
                    'success' => false,
                    'message' => 'Payment processor configuration error: API key not set'
                ];
            }

            \Log::info('Starting Stax Payment Process', [
                'payment_method_id' => $paymentMethodId,
                'api_url' => $this->staxApiUrl,
                'has_api_key' => !empty($this->staxApiKey)
            ]);

            $paymentData = $this->preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon);
            $response = $this->makeStaxApiCall($paymentData);

            return $this->handleStaxResponse($response);

        } catch (Exception $e) {
            \Log::error('Stax Payment Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Prepare payment data for Stax API
     */
    private function preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon)
    {
        $order = (object) $orderData;

        // Calculate totals
        $subtotal = $order->TtlAmt ?? 0;
        $discountAmount = $this->calculateDiscount($subtotal, $coupon);
        $tax = $this->calculateTax($subtotal);
        $shippingAmount = $this->calculateShipping($orderData);
        $total = $subtotal - $discountAmount + $tax + $shippingAmount;

        // Ensure minimum total amount (Stax might have minimum requirements)
        if ($total < 1.00) {
            $total = 1.00; // Set minimum $1.00 for testing
        }

        // Prepare line items
        $lineItems = $this->prepareLineItems($clubs);

        // Ensure we have at least one line item
        if (empty($lineItems)) {
            $lineItems = [
                [
                    'id' => 'default-item',
                    'item' => 'Golf Club Order',
                    'details' => 'Custom Golf Club Order',
                    'quantity' => 1,
                    'price' => $total
                ]
            ];
        }

        $paymentData = [
            'payment_method_id' => $paymentMethodId,
            'meta' => [
                'tax' => (float) $tax,
                'poNumber' => $order->OrdNo ?? 'ORD-' . time(),
                'shippingAmount' => (float) $shippingAmount,
                'payment_note' => $this->buildPaymentNote($order, $clubs),
                'subtotal' => (float) $subtotal,
                'lineItems' => $lineItems
            ],
            'total' => (float) $total,
            'pre_auth' => 0
        ];

        // Log payment data for debugging
        \Log::info('Prepared Payment Data', [
            'payment_method_id' => $paymentMethodId,
            'order_number' => $order->OrdNo ?? 'N/A',
            'total' => $total,
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shippingAmount,
            'discount' => $discountAmount,
            'line_items_count' => count($lineItems),
            'club_count' => is_array($clubs) ? count($clubs) : 0
        ]);

        return $paymentData;
    }

    /**
     * Calculate discount amount
     */
    private function calculateDiscount($subtotal, $coupon)
    {
        if (!$coupon || !isset($coupon['discount_percentage'])) {
            return 0;
        }

        return ($subtotal * $coupon['discount_percentage']) / 100;
    }

    /**
     * Calculate tax from order data or default calculation
     */
    private function calculateTax($orderData)
    {
        $order = (object) $orderData;

        // Use tax from order data if available
        if (isset($order->TaxAmt) && $order->TaxAmt > 0) {
            return (float) $order->TaxAmt;
        }

        // Use tax rate if available
        if (isset($order->TaxRate) && isset($order->TtlAmt)) {
            return (float) ($order->TtlAmt * ($order->TaxRate / 100));
        }

        // Default: no tax
        return 0;
    }

    /**
     * Calculate shipping from order data or default calculation
     */
    private function calculateShipping($orderData)
    {
        $order = (object) $orderData;

        // Use shipping amount from order data if available
        if (isset($order->ShippingAmt) && $order->ShippingAmt > 0) {
            return (float) $order->ShippingAmt;
        }

        // Use shipping cost if available
        if (isset($order->ShippingCost)) {
            return (float) $order->ShippingCost;
        }

        // Default: no shipping
        return 0;
    }

    /**
     * Prepare line items for Stax API
     */
    private function prepareLineItems($clubs)
    {
        $lineItems = [];

        foreach ($clubs as $club) {
            $clubObj = (object) $club;

            // Build dynamic item name and details
            $itemName = $this->buildItemName($clubObj);
            $itemDetails = $this->buildItemDetails($clubObj);

            $lineItems[] = [
                'id' => $clubObj->ClbDtlId ?? uniqid(),
                'item' => $itemName,
                'details' => $itemDetails,
                'quantity' => (int) ($clubObj->Qty ?? 1),
                'price' => (float) ($clubObj->EachAmt ?? 0)
            ];
        }

        return $lineItems;
    }

    /**
     * Build dynamic item name from club data
     */
    private function buildItemName($clubObj)
    {
        $parts = [];

        // Add model name if available
        if (!empty($clubObj->ModelName)) {
            $parts[] = $clubObj->ModelName;
        } elseif (!empty($clubObj->model_name)) {
            $parts[] = $clubObj->model_name;
        } else {
            $parts[] = 'Golf Club';
        }

        // Add category if available
        if (!empty($clubObj->CategoryName)) {
            $parts[] = '(' . $clubObj->CategoryName . ')';
        } elseif (!empty($clubObj->category_name)) {
            $parts[] = '(' . $clubObj->category_name . ')';
        }

        return implode(' ', $parts);
    }

    /**
     * Build dynamic item details from club data
     */
    private function buildItemDetails($clubObj)
    {
        $details = [];

        // Add shaft information
        if (!empty($clubObj->ShaftName)) {
            $details[] = 'Shaft: ' . $clubObj->ShaftName;
        } elseif (!empty($clubObj->shaft_name)) {
            $details[] = 'Shaft: ' . $clubObj->shaft_name;
        }

        // Add specifications
        if (!empty($clubObj->Loft)) {
            $details[] = 'Loft: ' . $clubObj->Loft . '°';
        }
        if (!empty($clubObj->Lie)) {
            $details[] = 'Lie: ' . $clubObj->Lie . '°';
        }
        if (!empty($clubObj->FaceAngle)) {
            $details[] = 'Face: ' . $clubObj->FaceAngle . '°';
        }

        // Add side/hand
        if (!empty($clubObj->Side)) {
            $details[] = 'Hand: ' . $clubObj->Side;
        }

        return !empty($details) ? implode(', ', $details) : 'Custom Golf Club';
    }

    /**
     * Build dynamic payment note
     */
    private function buildPaymentNote($order, $clubs)
    {
        $clubCount = is_array($clubs) ? count($clubs) : 0;
        $orderNumber = $order->OrdNo ?? 'N/A';

        if ($clubCount > 0) {
            return "Henry Griffitts Golf Club Order #{$orderNumber} - {$clubCount} club(s)";
        }

        return "Henry Griffitts Golf Order #{$orderNumber}";
    }

    /**
     * Make API call to Stax
     */
    private function makeStaxApiCall($paymentData)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Content-Type: application/json",
            "Authorization: Bearer " . $this->staxApiKey,
            "Accept: application/json"
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For testing only
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // For testing only

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log the response for debugging
        \Log::info('Stax Payment Response', [
            'http_code' => $httpCode,
            'response' => $response,
            'curl_error' => $error
        ]);

        if ($error) {
            throw new Exception("cURL Error: " . $error);
        }

        return [
            'response' => $response,
            'http_code' => $httpCode
        ];
    }

    /**
     * Handle Stax API response
     */
    private function handleStaxResponse($apiResponse)
    {
        $responseData = json_decode($apiResponse['response'], true);

        // Log response data for debugging
        \Log::info('Stax Response Processing', [
            'http_code' => $apiResponse['http_code'],
            'response_data' => $responseData,
            'json_decode_error' => json_last_error_msg()
        ]);

        // Check for JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => 'Invalid response format from payment processor: ' . json_last_error_msg()
            ];
        }

        // Check for successful response
        if ($apiResponse['http_code'] === 200 && isset($responseData['status']) && $responseData['status'] === 'success') {
            return [
                'success' => true,
                'transaction_id' => $responseData['id'] ?? null,
                'response' => $responseData
            ];
        }

        // Handle different error scenarios
        $errorMessage = 'Payment processing failed';

        if (isset($responseData['message'])) {
            $errorMessage = $responseData['message'];
        } elseif (isset($responseData['error'])) {
            $errorMessage = $responseData['error'];
        } elseif ($apiResponse['http_code'] === 401) {
            $errorMessage = 'Payment processor authentication failed';
        } elseif ($apiResponse['http_code'] === 400) {
            $errorMessage = 'Invalid payment data provided';
        } elseif ($apiResponse['http_code'] >= 500) {
            $errorMessage = 'Payment processor server error';
        }

        return [
            'success' => false,
            'message' => $errorMessage,
            'http_code' => $apiResponse['http_code'],
            'response_data' => $responseData
        ];
    }
}
