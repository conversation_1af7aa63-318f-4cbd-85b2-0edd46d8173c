<?php

namespace App\Services;

use Exception;

class PaymentService
{
    private $staxApiUrl;
    private $staxApiKey;

    public function __construct()
    {
        $this->staxApiUrl = config('services.stax.api_url', 'https://apiprod.fattlabs.com/charge');
        $this->staxApiKey = config('services.stax.api_key', env('STAX_API_KEY'));
    }

    /**
     * Process payment with Stax API
     */
    public function processStaxPayment($paymentMethodId, $orderData, $clubs, $coupon = null)
    {
        try {
            // Validate payment method ID
            if (empty($paymentMethodId)) {
                return [
                    'success' => false,
                    'message' => 'Payment method is required'
                ];
            }

            // Validate API key
            if (empty($this->staxApiKey)) {
                return [
                    'success' => false,
                    'message' => 'Payment processor configuration error: API key not set'
                ];
            }

            \Log::info('Starting Stax Payment Process', [
                'payment_method_id' => $paymentMethodId,
                'api_url' => $this->staxApiUrl,
                'has_api_key' => !empty($this->staxApiKey)
            ]);

            $paymentData = $this->preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon);
            $response = $this->makeStaxApiCall($paymentData);

            return $this->handleStaxResponse($response);

        } catch (Exception $e) {
            \Log::error('Stax Payment Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Prepare payment data for Stax API
     */
    private function preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon)
    {
        $order = (object) $orderData;

        // Calculate totals
        $subtotal = $order->TtlAmt ?? 0;
        $discountAmount = $this->calculateDiscount($subtotal, $coupon);
        $tax = $this->calculateTax($subtotal);
        $shippingAmount = $this->calculateShipping($orderData);
        $total = $subtotal - $discountAmount + $tax + $shippingAmount;

        // Ensure minimum total amount (Stax might have minimum requirements)
        if ($total < 1.00) {
            $total = 1.00; // Set minimum $1.00 for testing
        }

        // Prepare line items
        $lineItems = $this->prepareLineItems($clubs);

        // Ensure we have at least one line item
        if (empty($lineItems)) {
            $lineItems = [
                [
                    'id' => 'default-item',
                    'item' => 'Golf Club Order',
                    'details' => 'Custom Golf Club Order',
                    'quantity' => 1,
                    'price' => $total
                ]
            ];
        }

        $paymentData = [
            'payment_method_id' => $paymentMethodId,
            'meta' => [
                'tax' => (float) $tax,
                'poNumber' => $order->OrdNo ?? 'ORD-' . time(),
                'shippingAmount' => (float) $shippingAmount,
                'payment_note' => 'Golf Club Order Payment',
                'subtotal' => (float) $subtotal,
                'lineItems' => $lineItems
            ],
            'total' => (float) $total,
            'pre_auth' => 0
        ];

        // Log payment data for debugging
        \Log::info('Prepared Payment Data', [
            'payment_method_id' => $paymentMethodId,
            'total' => $total,
            'subtotal' => $subtotal,
            'line_items_count' => count($lineItems),
            'full_payment_data' => $paymentData
        ]);

        return $paymentData;
    }

    /**
     * Calculate discount amount
     */
    private function calculateDiscount($subtotal, $coupon)
    {
        if (!$coupon || !isset($coupon['discount_percentage'])) {
            return 0;
        }

        return ($subtotal * $coupon['discount_percentage']) / 100;
    }

    /**
     * Calculate tax (implement based on business rules)
     */
    private function calculateTax($subtotal)
    {
        // Implement tax calculation logic
        return 0;
    }

    /**
     * Calculate shipping (implement based on business rules)
     */
    private function calculateShipping($orderData)
    {
        // Implement shipping calculation logic
        return 0;
    }

    /**
     * Prepare line items for Stax API
     */
    private function prepareLineItems($clubs)
    {
        $lineItems = [];

        foreach ($clubs as $club) {
            $clubObj = (object) $club;
            $lineItems[] = [
                'id' => $clubObj->ClbDtlId ?? uniqid(),
                'item' => 'Golf Club',
                'details' => 'Custom Golf Club',
                'quantity' => $clubObj->Qty ?? 1,
                'price' => $clubObj->EachAmt ?? 0
            ];
        }

        return $lineItems;
    }

    /**
     * Make API call to Stax
     */
    public function makeStaxApiCall($paymentData)
    {
        // Log the request for debugging
        \Log::info('Stax Payment Request', [
            'url' => $this->staxApiUrl,
            'api_key_set' => !empty($this->staxApiKey),
            'payment_data' => $paymentData
        ]);

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $this->staxApiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => false,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($paymentData),
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json",
                "Authorization: Bearer " . $this->staxApiKey,
                "Accept: application/json"
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false, // For testing only
            CURLOPT_SSL_VERIFYHOST => false  // For testing only
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log the response for debugging
        \Log::info('Stax Payment Response', [
            'http_code' => $httpCode,
            'response' => $response,
            'curl_error' => $error
        ]);

        if ($error) {
            throw new Exception("cURL Error: " . $error);
        }

        return [
            'response' => $response,
            'http_code' => $httpCode
        ];
    }

    /**
     * Handle Stax API response
     */
    private function handleStaxResponse($apiResponse)
    {
        $responseData = json_decode($apiResponse['response'], true);

        // Log response data for debugging
        \Log::info('Stax Response Processing', [
            'http_code' => $apiResponse['http_code'],
            'response_data' => $responseData,
            'json_decode_error' => json_last_error_msg()
        ]);

        // Check for JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => 'Invalid response format from payment processor: ' . json_last_error_msg()
            ];
        }

        // Check for successful response
        if ($apiResponse['http_code'] === 200 && isset($responseData['status']) && $responseData['status'] === 'success') {
            return [
                'success' => true,
                'transaction_id' => $responseData['id'] ?? null,
                'response' => $responseData
            ];
        }

        // Handle different error scenarios
        $errorMessage = 'Payment processing failed';

        if (isset($responseData['message'])) {
            $errorMessage = $responseData['message'];
        } elseif (isset($responseData['error'])) {
            $errorMessage = $responseData['error'];
        } elseif ($apiResponse['http_code'] === 401) {
            $errorMessage = 'Payment processor authentication failed';
        } elseif ($apiResponse['http_code'] === 400) {
            $errorMessage = 'Invalid payment data provided';
        } elseif ($apiResponse['http_code'] >= 500) {
            $errorMessage = 'Payment processor server error';
        }

        return [
            'success' => false,
            'message' => $errorMessage,
            'http_code' => $apiResponse['http_code'],
            'response_data' => $responseData
        ];
    }
}
