<?php

require_once 'vendor/autoload.php';

use App\Services\PaymentService;

// Test data
$paymentMethodId = 'test-payment-method-id';
$orderData = [
    'TtlAmt' => 900.00,
    'OrdNo' => 'TEST-001'
];
$clubs = [
    [
        'ClbDtlId' => 1,
        'Qty' => 2,
        'EachAmt' => 450.00,
        'model' => (object) ['HeadDsgnDsc' => 'Test Club'],
        'category' => (object) ['ClbTypeDsc' => 'Driver']
    ]
];
$coupon = null;

try {
    $paymentService = new PaymentService();
    
    // Test the prepareLineItems method
    $reflection = new ReflectionClass($paymentService);
    $method = $reflection->getMethod('prepareLineItems');
    $method->setAccessible(true);
    
    $lineItems = $method->invoke($paymentService, $clubs);
    
    echo "Line Items:\n";
    print_r($lineItems);
    
    echo "\nTest completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
